import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:db_eats/services/notification_navigation_service.dart';
import 'package:db_eats/ui/orders/confirmed_orders_view.dart';
import 'package:db_eats/ui/catering/cateringrequest.dart';

/// Test page to verify notification navigation fixes
class NotificationFixTestPage extends StatefulWidget {
  const NotificationFixTestPage({super.key});

  @override
  State<NotificationFixTestPage> createState() => _NotificationFixTestPageState();
}

class _NotificationFixTestPageState extends State<NotificationFixTestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Fix Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Notification Navigation',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'These buttons simulate notification clicks to test the navigation fix:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            
            // Test Order Notification
            ElevatedButton(
              onPressed: () => _testOrderNotification(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text(
                'Test Order Notification (Type 2)',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            
            // Test Order Status Update
            ElevatedButton(
              onPressed: () => _testOrderStatusNotification(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text(
                'Test Order Status Notification (Type 3)',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            
            // Test Order In Progress
            ElevatedButton(
              onPressed: () => _testOrderInProgressNotification(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text(
                'Test Order In Progress (Type 4)',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            
            // Test Catering Accepted
            ElevatedButton(
              onPressed: () => _testCateringAcceptedNotification(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text(
                'Test Catering Accepted (Type 13)',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            
            // Test Catering Completed
            ElevatedButton(
              onPressed: () => _testCateringCompletedNotification(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text(
                'Test Catering Completed (Type 14)',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 30),
            
            // Direct navigation tests
            const Text(
              'Direct Navigation Tests:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            ElevatedButton(
              onPressed: () => _navigateDirectlyToOrderDetails(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text(
                'Direct Navigate to Order Details',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            
            ElevatedButton(
              onPressed: () => _navigateDirectlyToCatering(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text(
                'Direct Navigate to Catering',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _testOrderNotification(BuildContext context) {
    log('Testing Order Notification (Type 2)');
    _simulateNotificationClick(context, {
      'type': 2,
      'target_id': 12345,
      'user_id': 1,
      'user_role': 'customer'
    });
  }

  void _testOrderStatusNotification(BuildContext context) {
    log('Testing Order Status Notification (Type 3)');
    _simulateNotificationClick(context, {
      'type': 3,
      'target_id': 12346,
      'user_id': 1,
      'user_role': 'customer'
    });
  }

  void _testOrderInProgressNotification(BuildContext context) {
    log('Testing Order In Progress Notification (Type 4)');
    _simulateNotificationClick(context, {
      'type': 4,
      'target_id': 12347,
      'user_id': 1,
      'user_role': 'customer'
    });
  }

  void _testCateringAcceptedNotification(BuildContext context) {
    log('Testing Catering Accepted Notification (Type 13)');
    _simulateNotificationClick(context, {
      'type': 13,
      'target_id': 5001,
      'user_id': 1,
      'user_role': 'customer'
    });
  }

  void _testCateringCompletedNotification(BuildContext context) {
    log('Testing Catering Completed Notification (Type 14)');
    _simulateNotificationClick(context, {
      'type': 14,
      'target_id': 5002,
      'user_id': 1,
      'user_role': 'customer'
    });
  }

  void _simulateNotificationClick(BuildContext context, Map<String, dynamic> customData) {
    // Extract notification type and target ID
    final int? notificationType = customData['type'];
    final int? targetId = customData['target_id'];
    final int? userId = customData['user_id'];
    final String? userRole = customData['user_role'];

    log('Simulated notification - Type: $notificationType, Target ID: $targetId, User ID: $userId, User Role: $userRole');

    // Call the navigation service directly with the parsed data
    if (notificationType != null) {
      _navigateBasedOnType(context, notificationType, targetId);
    }
  }

  // Copy of the navigation logic from NotificationNavigationService for testing
  void _navigateBasedOnType(BuildContext context, int notificationType, int? targetId) {
    switch (notificationType) {
      case 2: // Order confirmed
      case 3: // Order status update
      case 4: // Order preparing/picking up/delivered
        log('Navigating to order details for order: $targetId');
        _navigateToOrderDetails(context, targetId);
        break;

      case 13: // Catering request accepted
        log('Navigating to catering requests - accepted tab');
        _navigateToCateringRequests(context, 1); // Accepted tab
        break;

      case 14: // Catering request completed
        log('Navigating to catering requests - past orders tab');
        _navigateToCateringRequests(context, 2); // Past Orders tab
        break;

      default:
        log('Unknown notification type: $notificationType');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unknown notification type: $notificationType'),
            backgroundColor: Colors.red,
          ),
        );
        break;
    }
  }

  void _navigateToOrderDetails(BuildContext context, int? orderId) {
    if (orderId == null || orderId == 0) {
      log('Invalid order ID: $orderId');
      return;
    }

    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(
        builder: (context) => ConfirmedOrdersView(
          orderId: orderId,
        ),
      ),
      (route) => false, // Clear entire stack
    );
  }

  void _navigateToCateringRequests(BuildContext context, int initialTab) {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(
        builder: (context) => CateringRequestsPage(
          initialTab: initialTab,
        ),
      ),
      (route) => false, // Clear entire stack
    );
  }

  void _navigateDirectlyToOrderDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ConfirmedOrdersView(
          orderId: 99999, // Test order ID
        ),
      ),
    );
  }

  void _navigateDirectlyToCatering(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CateringRequestsPage(
          initialTab: 1,
        ),
      ),
    );
  }
}
